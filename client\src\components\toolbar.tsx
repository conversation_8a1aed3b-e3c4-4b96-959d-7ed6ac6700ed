import { useTranslation } from 'react-i18next';
import { But<PERSON> } from '@/components/ui/button';
import { 
  FileText,
  Save, 
  Printer, 
  Search, 
  Clock,
  ListChecks
} from 'lucide-react';

interface ToolbarProps {
  wordCount: number;
  onNew: () => void;
  onSave: () => void;
  onPrint: () => void;
  onFindReplace: () => void;
  onInsertDateTime: () => void;
}

export default function Toolbar({
  wordCount,
  onNew,
  onSave,
  onPrint,
  onFindReplace,
  onInsertDateTime
}: ToolbarProps) {
  const { t } = useTranslation();

  return (
    <div className="bg-muted border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between py-2 sm:py-3 gap-2 sm:gap-0">
          {/* File Operations */}
          <div className="flex items-center space-x-1 sm:space-x-2 overflow-x-auto w-full sm:w-auto">
            <Button 
              onClick={onNew} 
              size="sm" 
              variant="outline"
              className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2 flex-shrink-0"
            >
              <FileText className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden xs:inline">{t('new')}</span>
            </Button>
            
            <Button onClick={onSave} size="sm" className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2 flex-shrink-0">
              <Save className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden xs:inline">{t('save')}</span>
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={onPrint}
              className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2 flex-shrink-0"
            >
              <Printer className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">{t('print')}</span>
            </Button>
            
            <Button 
              variant="ghost" 
              size="sm"
              onClick={onFindReplace}
              className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2 flex-shrink-0"
            >
              <Search className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden lg:inline">{t('findReplace')}</span>
            </Button>
            
            <Button 
              variant="ghost" 
              size="sm"
              onClick={onInsertDateTime}
              className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2 flex-shrink-0"
            >
              <Clock className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden lg:inline">{t('insertDate')}</span>
            </Button>
          </div>
          
          {/* Word Count Display */}
          <div className="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1 sm:py-2 bg-white rounded-md sm:rounded-lg border border-gray-200 flex-shrink-0 self-end sm:self-auto">
            <ListChecks className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            <span className="text-xs sm:text-sm font-medium text-foreground whitespace-nowrap">
              {wordCount} {t('words')}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
